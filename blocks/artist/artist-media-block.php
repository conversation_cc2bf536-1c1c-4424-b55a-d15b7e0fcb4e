<?php
// Add security headers for media embeds
add_action('wp_head', function() {
    if (is_singular('artist')) {
        echo '<meta http-equiv="Content-Security-Policy" content="frame-src https://www.youtube.com https://open.spotify.com; media-src https:; script-src \'self\' \'unsafe-inline\' https:;">' . "\n";
    }
});

$artist_id = $args['artist_id'] ?? get_the_ID();
$artist_name = get_the_title($artist_id);

// Get custom fields
$youtube_url = get_field('youtube_url', $artist_id);
$spotify_url = get_field('spotify_url', $artist_id);

// Extract YouTube video ID from URL
$youtube_video_id = '';
if ($youtube_url) {
    $youtube_video_id = extract_youtube_video_id($youtube_url);
}

// Extract Spotify embed URL from URL
$spotify_embed_url = '';
if ($spotify_url) {
    $spotify_embed_url = convert_spotify_url_to_embed($spotify_url);
}

/**
 * Extract YouTube video ID from various YouTube URL formats
 */
function extract_youtube_video_id($url) {
    // Force HTTPS for security
    $url = str_replace('http://', 'https://', $url);

    // Log the URL being processed for debugging
    error_log('Processing YouTube URL: ' . $url);

    $patterns = [
        // Standard youtube.com/watch?v= format with optional parameters
        '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/',
        // youtu.be/ format with optional parameters (like ?si=)
        '/youtu\.be\/([a-zA-Z0-9_-]{11})(?:\?.*)?/',
        // youtube.com/embed/ format
        '/youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/',
        // youtube.com/v/ format (legacy)
        '/youtube\.com\/v\/([a-zA-Z0-9_-]{11})/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            error_log('YouTube video ID extracted: ' . $matches[1]);
            return $matches[1];
        }
    }

    error_log('No YouTube video ID found in URL: ' . $url);
    return '';
}

/**
 * Convert Spotify URL to embed URL
 */
function convert_spotify_url_to_embed($url) {
    // Remove query parameters and extract the important part
    $url = strtok($url, '?');

    // Force HTTPS for security
    $url = str_replace('http://', 'https://', $url);

    // Convert open.spotify.com to embed format
    if (strpos($url, 'open.spotify.com') !== false) {
        $embed_url = str_replace('open.spotify.com', 'open.spotify.com/embed', $url);
        return $embed_url . '?utm_source=generator&theme=0';
    }

    return '';
}


?>

<section class="artistMediaBlock" data-init data-show-cursor>
    <div class="contentWrapper smaller">
        <h2 class="normalTitle">PLAYLIST</h2>
        
        <div class="mediaContainer">
            <?php if ($youtube_video_id): ?>
                <div class="youtubeContainer">
                    <div class="videoWrapper">
                        <!-- Debug info for development -->
                        <?php if (WP_DEBUG): ?>
                            <script>
                                console.log('YouTube URL from field: <?= esc_js($youtube_url) ?>');
                                console.log('Extracted YouTube video ID: <?= esc_js($youtube_video_id) ?>');
                                console.log('Final embed URL: https://www.youtube.com/embed/<?= esc_js($youtube_video_id) ?>');
                            </script>
                        <?php endif; ?>

                        <iframe
                            src="https://www.youtube.com/embed/<?= esc_attr($youtube_video_id) ?>?rel=0&showinfo=0&modestbranding=1&fs=0&autoplay=0&controls=1&disablekb=0&enablejsapi=0&iv_load_policy=3&loop=0&origin=<?= esc_url(home_url()) ?>"
                            frameborder="0"
                            allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            allowfullscreen
                            loading="lazy"
                            title="<?= esc_attr($artist_name) ?> - YouTube Video"
                            referrerpolicy="strict-origin-when-cross-origin">
                        </iframe>

                        <!-- Fallback link for cases where iframe truly fails -->
                        <noscript>
                            <div class="youtube-fallback">
                                <div class="fallback-message">
                                    <p>Watch on YouTube</p>
                                    <p><small>JavaScript is required to view embedded videos</small></p>
                                    <a href="https://www.youtube.com/watch?v=<?= esc_attr($youtube_video_id) ?>" target="_blank" class="button">
                                        <span class="innerText">Open on YouTube</span>
                                        <span class="arrows">
                                            <i class="icon-arrow-right-up"></i>
                                            <i class="icon-arrow-right-up"></i>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </noscript>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($spotify_embed_url): ?>
                <div class="spotifyContainer">
                    <iframe
                        src="<?= esc_url($spotify_embed_url) ?>"
                        width="100%"
                        height="100%"
                        frameborder="0"
                        allowfullscreen=""
                        allow="clipboard-write; encrypted-media; fullscreen; picture-in-picture"
                        referrerpolicy="no-referrer-when-downgrade"
                        loading="lazy"
                        onload="console.log('Spotify iframe loaded successfully')"
                        onerror="console.log('Spotify iframe failed to load')">
                    </iframe>

                    <!-- Fallback link if iframe fails -->
                    <noscript>
                        <div class="spotify-fallback">
                            <a href="<?= esc_url($spotify_url) ?>" target="_blank" class="button">
                                <span class="innerText">Open on Spotify</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        </div>
                    </noscript>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!$youtube_video_id && !$spotify_embed_url && current_user_can('edit_posts')): ?>
            <div class="noMediaMessage">
                <p>No media configured for this artist.</p>
                <p><small>Add YouTube URL and Spotify URL in the artist edit page.</small></p>

                <?php if (WP_DEBUG && ($youtube_url || $spotify_url)): ?>
                    <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border: 1px solid #ccc; font-family: monospace; font-size: 12px;">
                        <strong>Debug Info:</strong><br>
                        <?php if ($youtube_url): ?>
                            YouTube URL: <?= esc_html($youtube_url) ?><br>
                            Extracted ID: <?= esc_html($youtube_video_id ?: 'NONE') ?><br>
                        <?php endif; ?>
                        <?php if ($spotify_url): ?>
                            Spotify URL: <?= esc_html($spotify_url) ?><br>
                            Embed URL: <?= esc_html($spotify_embed_url ?: 'NONE') ?><br>
                        <?php endif; ?>
                    </div>
                    <script>
                        console.log('Debug - YouTube URL:', <?= json_encode($youtube_url) ?>);
                        console.log('Debug - YouTube ID:', <?= json_encode($youtube_video_id) ?>);
                        console.log('Debug - Spotify URL:', <?= json_encode($spotify_url) ?>);
                        console.log('Debug - Spotify Embed:', <?= json_encode($spotify_embed_url) ?>);
                    </script>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
